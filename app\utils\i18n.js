/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 <PERSON>ov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

// Simple internationalization utility
let currentLanguage = 'en';

// Translation dictionary
const translations = {
    en: {
        // Main Menu
        'Studies': 'Studies',
        'Editor': 'Editor',
        'Controlled Terminology': 'Controlled Terminology',
        'CDISC Library': 'CDISC Library',
        'Settings': 'Settings',
        'About': 'About',
        'Keyboard Shortcuts': 'Keyboard Shortcuts',
        'Update': 'Update',
        'Find in Page': 'Find in Page',
        'Search Studies': 'Search Studies',
        'History': 'History',
        'Save': 'Save',
        'Save As': 'Save As',
        'Print': 'Print',
        'Comments/Methods': 'Comments/Methods',
        'Tools': 'Tools',
        'Review Mode': 'Review Mode',
        'Quit': 'Quit',

        // Settings
        'General Settings': 'General Settings',
        'System': 'System',
        'User Name': 'User Name',
        'Controlled Terminology Folder': 'Controlled Terminology Folder',
        'Language': 'Language',
        'English': 'English',
        'Simplified Chinese': 'Simplified Chinese',

        // Common
        'Search': 'Search',
        'Cancel': 'Cancel',
        'OK': 'OK',
        'Yes': 'Yes',
        'No': 'No',
        'Close': 'Close',
        'Open': 'Open',
        'Delete': 'Delete',
        'Edit': 'Edit',
        'Add': 'Add',
        'Remove': 'Remove',
        'Copy': 'Copy',
        'Paste': 'Paste',
        'Cut': 'Cut',
        'Undo': 'Undo',
        'Redo': 'Redo',

        // File Menu
        'File': 'File',
        'Save As Define-XML 2.0': 'Save As Define-XML 2.0',
        'View': 'View',
        'Help': 'Help',

        // Editor
        'No Define-XML documents are selected for editing. Select a Define-XML document to edit on the': 'No Define-XML documents are selected for editing. Select a Define-XML document to edit on the',
        'page.': 'page.',
        'Loading Define-XML.': 'Loading Define-XML.',
        'Taking too long? Use Ctrl+M to open the menu.': 'Taking too long? Use Ctrl+M to open the menu.',

        // Editor Tabs
        'Standards': 'Standards',
        'Datasets': 'Datasets',
        'Variables': 'Variables',
        'Codelists': 'Codelists',
        'Coded Values': 'Coded Values',
        'Documents': 'Documents',
        'Result Displays': 'Result Displays',
        'Analysis Results': 'Analysis Results',
        'Review Comments': 'Review Comments',

        // Table Headers and Labels
        'Name': 'Name',
        'Label': 'Label',
        'Type': 'Type',
        'Length': 'Length',
        'Order': 'Order',
        'Description': 'Description',
        'Comment': 'Comment',
        'Method': 'Method',
        'Value': 'Value',
        'Code': 'Code',
        'Decode': 'Decode',
        'Status': 'Status',
        'Version': 'Version',
        'Date': 'Date',
        'Author': 'Author',
        'Title': 'Title',
        'Location': 'Location',
        'Format': 'Format',
        'Class': 'Class',
        'Structure': 'Structure',
        'Purpose': 'Purpose',
        'Keys': 'Keys',
        'Mandatory': 'Mandatory',
        'Repeating': 'Repeating',
        'Reference': 'Reference',
        'Standard': 'Standard',
        'Domain': 'Domain',
        'Variable': 'Variable',
        'Dataset': 'Dataset',
        'Codelist': 'Codelist',

        // Buttons and Actions
        'Update': 'Update',
        'Columns': 'Columns',
        'Import Metadata': 'Import Metadata',
        'Add Variable': 'Add Variable',
        'Add Dataset': 'Add Dataset',
        'New Variable': 'New Variable',
        'Options': 'Options',
        'Global Variables & Study OID': 'Global Variables & Study OID',
        'Study OID': 'Study OID',
        'Study Name': 'Study Name',
        'Protocol Name': 'Protocol Name',
        'Analysis Variables': 'Analysis Variables',
        'Add Reference Dataset': 'Add Reference Dataset',
        'Remove Variable': 'Remove Variable',

        // Search and Filter
        'Ctrl+F': 'Ctrl+F',
        'Expand VLM': 'Expand VLM',
        'Collapse VLM': 'Collapse VLM',
        'Filter': 'Filter',

        // Standards Tab
        'Global Variables & Study OID': 'Global Variables & Study OID',
        'Study Description': 'Study Description',
        'MetaDataVersion': 'MetaDataVersion',
        'Standard': 'Standard',
        'Controlled Terminology': 'Controlled Terminology',
        'ODM Attributes': 'ODM Attributes',
        'Other Attributes': 'Other Attributes',
        'Remove Standard': 'Remove Standard',
        'Analysis Results Metadata': 'Analysis Results Metadata',
        'File OID': 'File OID',
        'As Of Date Time': 'As Of Date Time',
        'Originator': 'Originator',
        'Stylesheet Location': 'Stylesheet Location',
        'Path to File': 'Path to File',
        'Last Modified': 'Last Modified',
        'ODM Attributes & Stylesheet location': 'ODM Attributes & Stylesheet location',
        'Sponsor Name': 'Sponsor Name',
        'Database Query Datetime': 'Database Query Datetime',
        'Visual Define-XML Editor Attributes': 'Visual Define-XML Editor Attributes',
        'Define-XML Name': 'Define-XML Name',
        'Define-XML Location': 'Define-XML Location',

        // XML Generation Comments
        'ItemGroup Definitions': 'ItemGroup Definitions',
        'ItemDef Definitions': 'ItemDef Definitions',
        'Codelist Definitions': 'Codelist Definitions',
        'Method Definitions': 'Method Definitions',
        'Comment Definitions': 'Comment Definitions',
        'Leaf Definitions': 'Leaf Definitions',
        'Analysis Result Display Definitions': 'Analysis Result Display Definitions',
    },
    'zh-CN': {
        // Main Menu
        'Studies': '研究',
        'Editor': '编辑器',
        'Controlled Terminology': '受控术语',
        'CDISC Library': 'CDISC 库',
        'Settings': '设置',
        'About': '关于',
        'Keyboard Shortcuts': '键盘快捷键',
        'Update': '更新',
        'Find in Page': '页面查找',
        'Search Studies': '搜索研究',
        'History': '历史记录',
        'Save': '保存',
        'Save As': '另存为',
        'Print': '打印',
        'Comments/Methods': '注释/方法',
        'Tools': '工具',
        'Review Mode': '审阅模式',
        'Quit': '退出',

        // Settings
        'General Settings': '常规设置',
        'System': '系统',
        'User Name': '用户名',
        'Controlled Terminology Folder': '受控术语文件夹',
        'Language': '语言',
        'English': '英语',
        'Simplified Chinese': '简体中文',

        // Common
        'Search': '搜索',
        'Cancel': '取消',
        'OK': '确定',
        'Yes': '是',
        'No': '否',
        'Close': '关闭',
        'Open': '打开',
        'Delete': '删除',
        'Edit': '编辑',
        'Add': '添加',
        'Remove': '移除',
        'Copy': '复制',
        'Paste': '粘贴',
        'Cut': '剪切',
        'Undo': '撤销',
        'Redo': '重做',

        // File Menu
        'File': '文件',
        'Save As Define-XML 2.0': '另存为 Define-XML 2.0',
        'View': '查看',
        'Help': '帮助',

        // Editor
        'No Define-XML documents are selected for editing. Select a Define-XML document to edit on the': '没有选择要编辑的 Define-XML 文档。请在',
        'page.': '页面选择要编辑的 Define-XML 文档。',
        'Loading Define-XML.': '正在加载 Define-XML。',
        'Taking too long? Use Ctrl+M to open the menu.': '加载时间过长？使用 Ctrl+M 打开菜单。',

        // Editor Tabs
        'Standards': '标准',
        'Datasets': '数据集',
        'Variables': '变量',
        'Codelists': '代码列表',
        'Coded Values': '编码值',
        'Documents': '文档',
        'Result Displays': '结果显示',
        'Analysis Results': '分析结果',
        'Review Comments': '审阅注释',

        // Table Headers and Labels
        'Name': '名称',
        'Label': '标签',
        'Type': '类型',
        'Length': '长度',
        'Order': '顺序',
        'Description': '描述',
        'Comment': '注释',
        'Method': '方法',
        'Value': '值',
        'Code': '代码',
        'Decode': '解码',
        'Status': '状态',
        'Version': '版本',
        'Date': '日期',
        'Author': '作者',
        'Title': '标题',
        'Location': '位置',
        'Format': '格式',
        'Class': '类别',
        'Structure': '结构',
        'Purpose': '用途',
        'Keys': '键',
        'Mandatory': '必填',
        'Repeating': '重复',
        'Reference': '引用',
        'Standard': '标准',
        'Domain': '域',
        'Variable': '变量',
        'Dataset': '数据集',
        'Codelist': '代码列表',

        // Buttons and Actions
        'Update': '更新',
        'Columns': '列',
        'Import Metadata': '导入元数据',
        'Add Variable': '添加变量',
        'Add Dataset': '添加数据集',
        'New Variable': '新变量',
        'Options': '选项',
        'Global Variables & Study OID': '全局变量和研究OID',
        'Study OID': '研究OID',
        'Study Name': '研究名称',
        'Protocol Name': '协议名称',
        'Analysis Variables': '分析变量',
        'Add Reference Dataset': '添加参考数据集',
        'Remove Variable': '移除变量',

        // Search and Filter
        'Ctrl+F': 'Ctrl+F',
        'Expand VLM': '展开VLM',
        'Collapse VLM': '折叠VLM',
        'Filter': '筛选',

        // Standards Tab
        'Global Variables & Study OID': '全局变量和研究OID',
        'Study Description': '研究描述',
        'MetaDataVersion': '元数据版本',
        'Standard': '标准',
        'Controlled Terminology': '受控术语',
        'ODM Attributes': 'ODM属性',
        'Other Attributes': '其他属性',
        'Remove Standard': '移除标准',
        'Analysis Results Metadata': '分析结果元数据',
        'File OID': '文件OID',
        'As Of Date Time': '截止日期时间',
        'Originator': '创建者',
        'Stylesheet Location': '样式表位置',
        'Path to File': '文件路径',
        'Last Modified': '最后修改',
        'ODM Attributes & Stylesheet location': 'ODM属性和样式表位置',
        'Sponsor Name': '申办方名称',
        'Database Query Datetime': '数据库查询日期时间',
        'Visual Define-XML Editor Attributes': 'Visual Define-XML编辑器属性',
        'Define-XML Name': 'Define-XML名称',
        'Define-XML Location': 'Define-XML位置',

        // XML Generation Comments
        'ItemGroup Definitions': '数据集定义',
        'ItemDef Definitions': '数据项定义',
        'Codelist Definitions': '代码列表定义',
        'Method Definitions': '方法定义',
        'Comment Definitions': '注释定义',
        'Leaf Definitions': '叶子定义',
        'Analysis Result Display Definitions': '分析结果显示定义',
    }
};

// Set current language
export const setLanguage = (language) => {
    currentLanguage = language;
};

// Get current language
export const getCurrentLanguage = () => {
    return currentLanguage;
};

// Translation function
export const t = (key, defaultValue = null) => {
    const translation = translations[currentLanguage] && translations[currentLanguage][key];
    return translation || defaultValue || key;
};

// Initialize language from settings
export const initLanguage = (language) => {
    if (language && translations[language]) {
        setLanguage(language);
    }
};

export default { t, setLanguage, getCurrentLanguage, initLanguage };
